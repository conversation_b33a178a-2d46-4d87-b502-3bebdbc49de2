import React from 'react';
import {RouterDom} from 'react-router-dom';
import mongoose from 'mongoose';

PORT = 3000;
app.post('/', (req, res) => {
    res.json({
            'messaage' : 'Api is working',
            'status' : 'true'
        }
    )
})

app.get('/', (req, res) => {
    res.json({
            'messaage' : 'Api is working',
            'status' : 'true'
        }
    )
})

app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
});

export default App;